# Exercise 2: Advanced Job Search 🔍

## 🎯 Objective

Build a sophisticated job search system with multiple filters, sorting options, real-time results, and advanced query capabilities. This exercise will help you practice complex database queries, AJAX interactions, and creating intuitive user interfaces.

## 📋 Prerequisites

- Completed Lessons 1-13 (Setup, Backend, and Frontend Development)
- Understanding of Django QuerySets and database optimization
- Basic knowledge of JavaScript and AJAX
- Familiarity with Stimulus controllers

## 🎨 What You'll Build

### **Search Features**
- Text search across job titles and descriptions
- Category and skill-based filtering
- Budget range filtering
- Location-based search
- Date range filtering
- Experience level requirements
- Project duration filtering

### **Advanced Functionality**
- Real-time search results (no page refresh)
- Search suggestions and autocomplete
- Saved searches
- Search result sorting
- Pagination with infinite scroll
- Search analytics

## 🛠️ Implementation Steps

### **Step 1: Enhanced Job Model and Search Backend**

```python
# backend/apps/jobs/search.py
from django.db.models import Q, Count, Avg
from django.contrib.postgres.search import SearchVector, SearchQuery, SearchRank
from .models import Job

class JobSearchEngine:
    def __init__(self, queryset=None):
        self.queryset = queryset or Job.objects.published()
    
    def search(self, query_params):
        """Main search method that applies all filters"""
        queryset = self.queryset
        
        # Text search
        if query_params.get('q'):
            queryset = self._apply_text_search(queryset, query_params['q'])
        
        # Category filter
        if query_params.get('category'):
            queryset = queryset.filter(category__slug=query_params['category'])
        
        # Skills filter
        if query_params.getlist('skills'):
            queryset = queryset.filter(skills__id__in=query_params.getlist('skills')).distinct()
        
        # Budget range
        if query_params.get('min_budget'):
            queryset = queryset.filter(budget_min__gte=query_params['min_budget'])
        if query_params.get('max_budget'):
            queryset = queryset.filter(budget_max__lte=query_params['max_budget'])
        
        # Location
        if query_params.get('location'):
            queryset = self._apply_location_filter(queryset, query_params['location'])
        
        # Experience level
        if query_params.get('experience_level'):
            queryset = self._apply_experience_filter(queryset, query_params['experience_level'])
        
        # Project duration
        if query_params.get('duration'):
            queryset = self._apply_duration_filter(queryset, query_params['duration'])
        
        # Date range
        if query_params.get('posted_since'):
            queryset = self._apply_date_filter(queryset, query_params['posted_since'])
        
        # Apply sorting
        queryset = self._apply_sorting(queryset, query_params.get('sort', 'relevance'))
        
        return queryset
    
    def _apply_text_search(self, queryset, search_term):
        """Apply full-text search using PostgreSQL"""
        search_vector = SearchVector('title', weight='A') + SearchVector('description', weight='B')
        search_query = SearchQuery(search_term)
        
        return queryset.annotate(
            search=search_vector,
            rank=SearchRank(search_vector, search_query)
        ).filter(search=search_query).order_by('-rank')
    
    def _apply_location_filter(self, queryset, location):
        """Filter by location (remote, specific city, etc.)"""
        if location.lower() == 'remote':
            return queryset.filter(is_remote=True)
        else:
            return queryset.filter(
                Q(location__icontains=location) | Q(is_remote=True)
            )
    
    def _apply_experience_filter(self, queryset, experience_level):
        """Filter by required experience level"""
        experience_mapping = {
            'entry': Q(experience_required__lte=1),
            'intermediate': Q(experience_required__gte=2, experience_required__lte=5),
            'senior': Q(experience_required__gte=5),
        }
        
        if experience_level in experience_mapping:
            return queryset.filter(experience_mapping[experience_level])
        
        return queryset
    
    def _apply_duration_filter(self, queryset, duration):
        """Filter by project duration"""
        duration_mapping = {
            'short': Q(duration_weeks__lte=4),
            'medium': Q(duration_weeks__gte=4, duration_weeks__lte=12),
            'long': Q(duration_weeks__gte=12),
        }
        
        if duration in duration_mapping:
            return queryset.filter(duration_mapping[duration])
        
        return queryset
    
    def _apply_date_filter(self, queryset, posted_since):
        """Filter by posting date"""
        from datetime import datetime, timedelta
        
        date_mapping = {
            'today': timedelta(days=1),
            'week': timedelta(weeks=1),
            'month': timedelta(days=30),
        }
        
        if posted_since in date_mapping:
            cutoff_date = datetime.now() - date_mapping[posted_since]
            return queryset.filter(created_at__gte=cutoff_date)
        
        return queryset
    
    def _apply_sorting(self, queryset, sort_option):
        """Apply sorting to results"""
        sort_mapping = {
            'relevance': '-created_at',  # Default if no text search
            'newest': '-created_at',
            'oldest': 'created_at',
            'budget_high': '-budget_max',
            'budget_low': 'budget_min',
            'applications': '-applications_count',
        }
        
        # Annotate with applications count if needed
        if sort_option == 'applications':
            queryset = queryset.annotate(applications_count=Count('deal'))
        
        return queryset.order_by(sort_mapping.get(sort_option, '-created_at'))
    
    def get_search_suggestions(self, query, limit=5):
        """Get search suggestions based on partial query"""
        suggestions = []
        
        # Job title suggestions
        title_matches = Job.objects.filter(
            title__icontains=query
        ).values_list('title', flat=True).distinct()[:limit]
        
        suggestions.extend([{'type': 'title', 'text': title} for title in title_matches])
        
        # Skill suggestions
        from taxonomy.models import Skill
        skill_matches = Skill.objects.filter(
            name__icontains=query
        ).values_list('name', flat=True)[:limit]
        
        suggestions.extend([{'type': 'skill', 'text': skill} for skill in skill_matches])
        
        return suggestions[:limit]

# backend/apps/jobs/views.py
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.template.loader import render_to_string
from .search import JobSearchEngine

class JobSearchView(ListView):
    model = Job
    template_name = 'jobs/search.html'
    context_object_name = 'jobs'
    paginate_by = 20
    
    def get_queryset(self):
        search_engine = JobSearchEngine()
        return search_engine.search(self.request.GET)
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Add filter options
        from taxonomy.models import Category, Skill
        context['categories'] = Category.objects.all()
        context['skills'] = Skill.objects.all()
        context['search_params'] = self.request.GET
        
        # Add search statistics
        context['total_results'] = self.get_queryset().count()
        
        return context

def job_search_ajax(request):
    """AJAX endpoint for real-time search"""
    search_engine = JobSearchEngine()
    jobs = search_engine.search(request.GET)
    
    # Pagination
    page = request.GET.get('page', 1)
    paginator = Paginator(jobs, 20)
    page_obj = paginator.get_page(page)
    
    # Render results
    html = render_to_string('jobs/search_results.html', {
        'jobs': page_obj,
        'page_obj': page_obj,
    }, request=request)
    
    return JsonResponse({
        'html': html,
        'has_next': page_obj.has_next(),
        'total_results': paginator.count,
        'current_page': page_obj.number,
        'total_pages': paginator.num_pages,
    })

def search_suggestions(request):
    """AJAX endpoint for search suggestions"""
    query = request.GET.get('q', '')
    if len(query) < 2:
        return JsonResponse({'suggestions': []})
    
    search_engine = JobSearchEngine()
    suggestions = search_engine.get_search_suggestions(query)
    
    return JsonResponse({'suggestions': suggestions})
```

### **Step 2: Advanced Search Form and Frontend**

```html
<!-- backend/templates/jobs/search.html -->
{% extends 'base.html' %}
{% load widget_tweaks %}

{% block title %}Search Jobs - Crelancer{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Search Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Find Your Perfect Job</h1>
        <p class="text-gray-600 mt-2">{{ total_results }} jobs found</p>
    </div>

    <!-- Search Form -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8"
         data-controller="job-search"
         data-job-search-url-value="{% url 'jobs:search_ajax' %}"
         data-job-search-suggestions-url-value="{% url 'jobs:search_suggestions' %}">
        
        <form data-job-search-target="form" data-action="submit->job-search#search">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <!-- Text Search -->
                <div class="lg:col-span-2 relative">
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-1">
                        Search Jobs
                    </label>
                    <div class="relative">
                        <input type="text" 
                               name="q" 
                               id="search"
                               value="{{ search_params.q }}"
                               placeholder="Job title, skills, or keywords..."
                               class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                               data-job-search-target="searchInput"
                               data-action="input->job-search#handleSearchInput keydown->job-search#handleKeydown">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                            </svg>
                        </div>
                        
                        <!-- Search Suggestions -->
                        <div data-job-search-target="suggestions" 
                             class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 hidden">
                        </div>
                    </div>
                </div>

                <!-- Category Filter -->
                <div>
                    <label for="category" class="block text-sm font-medium text-gray-700 mb-1">
                        Category
                    </label>
                    <select name="category" 
                            id="category"
                            class="block w-full py-2 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            data-action="change->job-search#search">
                        <option value="">All Categories</option>
                        {% for category in categories %}
                        <option value="{{ category.slug }}" 
                                {% if search_params.category == category.slug %}selected{% endif %}>
                            {{ category.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Location Filter -->
                <div>
                    <label for="location" class="block text-sm font-medium text-gray-700 mb-1">
                        Location
                    </label>
                    <input type="text" 
                           name="location" 
                           id="location"
                           value="{{ search_params.location }}"
                           placeholder="City or 'Remote'"
                           class="block w-full py-2 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                           data-action="input->job-search#debouncedSearch">
                </div>
            </div>

            <!-- Advanced Filters (Collapsible) -->
            <div class="mt-6">
                <button type="button" 
                        class="flex items-center text-sm font-medium text-blue-600 hover:text-blue-800"
                        data-action="click->job-search#toggleAdvancedFilters">
                    <span data-job-search-target="advancedToggleText">Show Advanced Filters</span>
                    <svg data-job-search-target="advancedToggleIcon" class="ml-1 h-4 w-4 transform transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                    </svg>
                </button>
                
                <div data-job-search-target="advancedFilters" class="hidden mt-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <!-- Budget Range -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Budget Range</label>
                        <div class="flex space-x-2">
                            <input type="number" 
                                   name="min_budget" 
                                   placeholder="Min"
                                   value="{{ search_params.min_budget }}"
                                   class="block w-full py-2 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                   data-action="input->job-search#debouncedSearch">
                            <input type="number" 
                                   name="max_budget" 
                                   placeholder="Max"
                                   value="{{ search_params.max_budget }}"
                                   class="block w-full py-2 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                   data-action="input->job-search#debouncedSearch">
                        </div>
                    </div>

                    <!-- Experience Level -->
                    <div>
                        <label for="experience_level" class="block text-sm font-medium text-gray-700 mb-1">
                            Experience Level
                        </label>
                        <select name="experience_level" 
                                id="experience_level"
                                class="block w-full py-2 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                data-action="change->job-search#search">
                            <option value="">Any Level</option>
                            <option value="entry" {% if search_params.experience_level == 'entry' %}selected{% endif %}>Entry Level</option>
                            <option value="intermediate" {% if search_params.experience_level == 'intermediate' %}selected{% endif %}>Intermediate</option>
                            <option value="senior" {% if search_params.experience_level == 'senior' %}selected{% endif %}>Senior</option>
                        </select>
                    </div>

                    <!-- Project Duration -->
                    <div>
                        <label for="duration" class="block text-sm font-medium text-gray-700 mb-1">
                            Project Duration
                        </label>
                        <select name="duration" 
                                id="duration"
                                class="block w-full py-2 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                data-action="change->job-search#search">
                            <option value="">Any Duration</option>
                            <option value="short" {% if search_params.duration == 'short' %}selected{% endif %}>Short (< 1 month)</option>
                            <option value="medium" {% if search_params.duration == 'medium' %}selected{% endif %}>Medium (1-3 months)</option>
                            <option value="long" {% if search_params.duration == 'long' %}selected{% endif %}>Long (3+ months)</option>
                        </select>
                    </div>

                    <!-- Posted Since -->
                    <div>
                        <label for="posted_since" class="block text-sm font-medium text-gray-700 mb-1">
                            Posted Since
                        </label>
                        <select name="posted_since" 
                                id="posted_since"
                                class="block w-full py-2 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                data-action="change->job-search#search">
                            <option value="">Any Time</option>
                            <option value="today" {% if search_params.posted_since == 'today' %}selected{% endif %}>Today</option>
                            <option value="week" {% if search_params.posted_since == 'week' %}selected{% endif %}>This Week</option>
                            <option value="month" {% if search_params.posted_since == 'month' %}selected{% endif %}>This Month</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Skills Filter -->
            <div class="mt-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Skills</label>
                <div class="flex flex-wrap gap-2" data-job-search-target="skillsContainer">
                    {% for skill in skills %}
                    <label class="inline-flex items-center">
                        <input type="checkbox" 
                               name="skills" 
                               value="{{ skill.id }}"
                               {% if skill.id|stringformat:"s" in search_params.skills %}checked{% endif %}
                               class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                               data-action="change->job-search#search">
                        <span class="ml-2 text-sm text-gray-700">{{ skill.name }}</span>
                    </label>
                    {% endfor %}
                </div>
            </div>
        </form>
    </div>

    <!-- Results Section -->
    <div class="flex flex-col lg:flex-row gap-8">
        <!-- Filters Sidebar (Mobile: Hidden by default) -->
        <div class="lg:w-64 flex-shrink-0">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="font-medium text-gray-900">Sort Results</h3>
                </div>
                
                <select name="sort" 
                        class="block w-full py-2 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        data-action="change->job-search#search">
                    <option value="relevance" {% if search_params.sort == 'relevance' %}selected{% endif %}>Most Relevant</option>
                    <option value="newest" {% if search_params.sort == 'newest' %}selected{% endif %}>Newest First</option>
                    <option value="budget_high" {% if search_params.sort == 'budget_high' %}selected{% endif %}>Highest Budget</option>
                    <option value="budget_low" {% if search_params.sort == 'budget_low' %}selected{% endif %}>Lowest Budget</option>
                    <option value="applications" {% if search_params.sort == 'applications' %}selected{% endif %}>Most Applications</option>
                </select>

                <!-- Active Filters -->
                <div class="mt-6" data-job-search-target="activeFilters">
                    <!-- Will be populated by JavaScript -->
                </div>
            </div>
        </div>

        <!-- Results -->
        <div class="flex-1">
            <!-- Loading State -->
            <div data-job-search-target="loading" class="hidden">
                <div class="flex items-center justify-center py-8">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    <span class="ml-2 text-gray-600">Searching...</span>
                </div>
            </div>

            <!-- Results Container -->
            <div data-job-search-target="results">
                {% include 'jobs/search_results.html' %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Additional JavaScript for enhanced functionality
document.addEventListener('DOMContentLoaded', function() {
    // Initialize search on page load
    const searchController = application.getControllerForElementAndIdentifier(
        document.querySelector('[data-controller="job-search"]'), 
        'job-search'
    );
    
    // Handle browser back/forward
    window.addEventListener('popstate', function(event) {
        if (event.state && event.state.searchParams) {
            searchController.loadSearchParams(event.state.searchParams);
        }
    });
});
</script>
{% endblock %}
```

### **Step 3: Stimulus Controller for Real-time Search**

```javascript
// frontend/src/controllers/job_search_controller.js
import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = [
    "form", "results", "loading", "searchInput", "suggestions",
    "advancedFilters", "advancedToggleText", "advancedToggleIcon",
    "skillsContainer", "activeFilters"
  ]
  
  static values = {
    url: String,
    suggestionsUrl: String,
    debounceDelay: { type: Number, default: 300 }
  }
  
  connect() {
    this.timeout = null
    this.suggestionsTimeout = null
    this.abortController = null
    this.advancedFiltersVisible = false
    
    // Initialize active filters display
    this.updateActiveFilters()
    
    // Close suggestions when clicking outside
    document.addEventListener('click', this.closeSuggestions.bind(this))
  }
  
  disconnect() {
    if (this.abortController) {
      this.abortController.abort()
    }
    document.removeEventListener('click', this.closeSuggestions.bind(this))
  }
  
  // Main search method
  search(event) {
    if (event) event.preventDefault()
    
    clearTimeout(this.timeout)
    this.timeout = setTimeout(() => {
      this.performSearch()
    }, 100) // Short delay for immediate feedback
  }
  
  // Debounced search for input fields
  debouncedSearch() {
    clearTimeout(this.timeout)
    this.timeout = setTimeout(() => {
      this.performSearch()
    }, this.debounceDelayValue)
  }
  
  // Perform the actual search
  async performSearch(loadMore = false) {
    const formData = new FormData(this.formTarget)
    const params = new URLSearchParams(formData)
    
    // Add page parameter for pagination
    if (loadMore) {
      const currentPage = parseInt(params.get('page') || '1')
      params.set('page', currentPage + 1)
    } else {
      params.set('page', '1')
    }
    
    // Cancel previous request
    if (this.abortController) {
      this.abortController.abort()
    }
    this.abortController = new AbortController()
    
    // Show loading state
    this.showLoading()
    
    try {
      const response = await fetch(`${this.urlValue}?${params}`, {
        headers: {
          'Accept': 'application/json',
          'X-Requested-With': 'XMLHttpRequest'
        },
        signal: this.abortController.signal
      })
      
      if (!response.ok) throw new Error('Search failed')
      
      const data = await response.json()
      
      if (loadMore) {
        this.appendResults(data.html)
      } else {
        this.replaceResults(data.html)
      }
      
      // Update URL without page reload
      this.updateURL(params)
      
      // Update active filters
      this.updateActiveFilters()
      
    } catch (error) {
      if (error.name !== 'AbortError') {
        console.error('Search error:', error)
        this.showError('Search failed. Please try again.')
      }
    } finally {
      this.hideLoading()
    }
  }
  
  // Handle search input with suggestions
  handleSearchInput(event) {
    const query = event.target.value.trim()
    
    if (query.length >= 2) {
      this.showSuggestions(query)
    } else {
      this.hideSuggestions()
    }
    
    this.debouncedSearch()
  }
  
  // Show search suggestions
  async showSuggestions(query) {
    clearTimeout(this.suggestionsTimeout)
    
    this.suggestionsTimeout = setTimeout(async () => {
      try {
        const response = await fetch(`${this.suggestionsUrlValue}?q=${encodeURIComponent(query)}`)
        const data = await response.json()
        
        if (data.suggestions && data.suggestions.length > 0) {
          this.renderSuggestions(data.suggestions)
          this.suggestionsTarget.classList.remove('hidden')
        } else {
          this.hideSuggestions()
        }
      } catch (error) {
        console.error('Suggestions error:', error)
      }
    }, 200)
  }
  
  // Render suggestions dropdown
  renderSuggestions(suggestions) {
    const html = suggestions.map(suggestion => `
      <div class="px-4 py-2 hover:bg-gray-100 cursor-pointer border-b border-gray-100 last:border-b-0"
           data-action="click->job-search#selectSuggestion"
           data-suggestion-text="${suggestion.text}"
           data-suggestion-type="${suggestion.type}">
        <div class="flex items-center">
          <span class="text-xs text-gray-500 uppercase mr-2">${suggestion.type}</span>
          <span class="text-gray-900">${suggestion.text}</span>
        </div>
      </div>
    `).join('')
    
    this.suggestionsTarget.innerHTML = html
  }
  
  // Select a suggestion
  selectSuggestion(event) {
    const text = event.currentTarget.dataset.suggestionText
    const type = event.currentTarget.dataset.suggestionType
    
    if (type === 'skill') {
      // Add skill to skills filter
      this.addSkillFilter(text)
    } else {
      // Set as search query
      this.searchInputTarget.value = text
    }
    
    this.hideSuggestions()
    this.performSearch()
  }
  
  // Add skill filter
  addSkillFilter(skillName) {
    const skillCheckboxes = this.skillsContainerTarget.querySelectorAll('input[type="checkbox"]')
    
    for (const checkbox of skillCheckboxes) {
      const label = checkbox.nextElementSibling
      if (label && label.textContent.trim().toLowerCase() === skillName.toLowerCase()) {
        checkbox.checked = true
        break
      }
    }
  }
  
  // Handle keyboard navigation in suggestions
  handleKeydown(event) {
    if (!this.suggestionsTarget.classList.contains('hidden')) {
      const suggestions = this.suggestionsTarget.querySelectorAll('[data-action*="selectSuggestion"]')
      const currentActive = this.suggestionsTarget.querySelector('.bg-blue-100')
      
      if (event.key === 'ArrowDown') {
        event.preventDefault()
        const next = currentActive ? currentActive.nextElementSibling : suggestions[0]
        if (next) {
          if (currentActive) currentActive.classList.remove('bg-blue-100')
          next.classList.add('bg-blue-100')
        }
      } else if (event.key === 'ArrowUp') {
        event.preventDefault()
        const prev = currentActive ? currentActive.previousElementSibling : suggestions[suggestions.length - 1]
        if (prev) {
          if (currentActive) currentActive.classList.remove('bg-blue-100')
          prev.classList.add('bg-blue-100')
        }
      } else if (event.key === 'Enter' && currentActive) {
        event.preventDefault()
        currentActive.click()
      } else if (event.key === 'Escape') {
        this.hideSuggestions()
      }
    }
  }
  
  // Toggle advanced filters
  toggleAdvancedFilters() {
    this.advancedFiltersVisible = !this.advancedFiltersVisible
    
    if (this.advancedFiltersVisible) {
      this.advancedFiltersTarget.classList.remove('hidden')
      this.advancedToggleTextTarget.textContent = 'Hide Advanced Filters'
      this.advancedToggleIconTarget.style.transform = 'rotate(180deg)'
    } else {
      this.advancedFiltersTarget.classList.add('hidden')
      this.advancedToggleTextTarget.textContent = 'Show Advanced Filters'
      this.advancedToggleIconTarget.style.transform = 'rotate(0deg)'
    }
  }
  
  // Update active filters display
  updateActiveFilters() {
    const formData = new FormData(this.formTarget)
    const activeFilters = []
    
    // Check each filter type
    for (const [key, value] of formData.entries()) {
      if (value && key !== 'page' && key !== 'csrfmiddlewaretoken') {
        if (key === 'skills') {
          // Handle multiple skills
          const skillName = this.getSkillName(value)
          if (skillName) {
            activeFilters.push({
              key: key,
              value: value,
              display: skillName,
              type: 'skill'
            })
          }
        } else {
          activeFilters.push({
            key: key,
            value: value,
            display: this.getFilterDisplay(key, value),
            type: 'filter'
          })
        }
      }
    }
    
    this.renderActiveFilters(activeFilters)
  }
  
  // Render active filters
  renderActiveFilters(filters) {
    if (filters.length === 0) {
      this.activeFiltersTarget.innerHTML = ''
      return
    }
    
    const html = `
      <h4 class="text-sm font-medium text-gray-900 mb-2">Active Filters</h4>
      <div class="space-y-1">
        ${filters.map(filter => `
          <div class="flex items-center justify-between text-sm">
            <span class="text-gray-600">${filter.display}</span>
            <button type="button" 
                    class="text-red-600 hover:text-red-800 ml-2"
                    data-action="click->job-search#removeFilter"
                    data-filter-key="${filter.key}"
                    data-filter-value="${filter.value}">
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
              </svg>
            </button>
          </div>
        `).join('')}
      </div>
      <button type="button" 
              class="text-sm text-blue-600 hover:text-blue-800 mt-2"
              data-action="click->job-search#clearAllFilters">
        Clear All Filters
      </button>
    `
    
    this.activeFiltersTarget.innerHTML = html
  }
  
  // Remove individual filter
  removeFilter(event) {
    const key = event.currentTarget.dataset.filterKey
    const value = event.currentTarget.dataset.filterValue
    
    // Find and uncheck/clear the filter
    const element = this.formTarget.querySelector(`[name="${key}"][value="${value}"], [name="${key}"]`)
    if (element) {
      if (element.type === 'checkbox') {
        element.checked = false
      } else {
        element.value = ''
      }
    }
    
    this.performSearch()
  }
  
  // Clear all filters
  clearAllFilters() {
    this.formTarget.reset()
    this.performSearch()
  }
  
  // Helper methods
  getFilterDisplay(key, value) {
    const displayMap = {
      'q': `Search: "${value}"`,
      'category': `Category: ${value}`,
      'location': `Location: ${value}`,
      'min_budget': `Min Budget: $${value}`,
      'max_budget': `Max Budget: $${value}`,
      'experience_level': `Experience: ${value}`,
      'duration': `Duration: ${value}`,
      'posted_since': `Posted: ${value}`,
    }
    
    return displayMap[key] || `${key}: ${value}`
  }
  
  getSkillName(skillId) {
    const checkbox = this.formTarget.querySelector(`input[name="skills"][value="${skillId}"]`)
    return checkbox ? checkbox.nextElementSibling.textContent.trim() : null
  }
  
  // UI state methods
  showLoading() {
    this.loadingTarget.classList.remove('hidden')
  }
  
  hideLoading() {
    this.loadingTarget.classList.add('hidden')
  }
  
  hideSuggestions() {
    this.suggestionsTarget.classList.add('hidden')
  }
  
  closeSuggestions(event) {
    if (!this.suggestionsTarget.contains(event.target) && 
        !this.searchInputTarget.contains(event.target)) {
      this.hideSuggestions()
    }
  }
  
  replaceResults(html) {
    this.resultsTarget.innerHTML = html
  }
  
  appendResults(html) {
    this.resultsTarget.insertAdjacentHTML('beforeend', html)
  }
  
  showError(message) {
    this.resultsTarget.innerHTML = `
      <div class="text-center py-8">
        <div class="text-red-600 mb-2">${message}</div>
        <button class="text-blue-600 hover:text-blue-800" 
                data-action="click->job-search#performSearch">
          Try Again
        </button>
      </div>
    `
  }
  
  updateURL(params) {
    const url = new URL(window.location)
    url.search = params.toString()
    window.history.pushState({ searchParams: params.toString() }, '', url)
  }
}
```

### **Step 4: Search Results Template**

```html
<!-- backend/templates/jobs/search_results.html -->
{% load humanize %}

{% if jobs %}
    <div class="space-y-6">
        {% for job in jobs %}
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
            <div class="flex justify-between items-start">
                <div class="flex-1">
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">
                        <a href="{% url 'jobs:detail' job.id %}" class="hover:text-blue-600">
                            {{ job.title }}
                        </a>
                    </h3>
                    
                    <div class="flex items-center text-sm text-gray-500 mb-3 space-x-4">
                        <span class="flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
                            </svg>
                            {% if job.is_remote %}Remote{% else %}{{ job.location|default:"Not specified" }}{% endif %}
                        </span>
                        <span class="flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
                            </svg>
                            {{ job.created_at|timesince }} ago
                        </span>
                        <span class="flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                            {{ job.deal_set.count }} applications
                        </span>
                    </div>
                    
                    <p class="text-gray-700 mb-4">{{ job.description|truncatewords:30 }}</p>
                    
                    <div class="flex flex-wrap gap-2 mb-4">
                        {% for skill in job.skills.all|slice:":5" %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {{ skill.name }}
                        </span>
                        {% endfor %}
                        {% if job.skills.count > 5 %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            +{{ job.skills.count|add:"-5" }} more
                        </span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="ml-6 text-right">
                    <div class="text-lg font-semibold text-gray-900 mb-2">
                        {% if job.budget_min == job.budget_max %}
                            ${{ job.budget_min|floatformat:0|intcomma }}
                        {% else %}
                            ${{ job.budget_min|floatformat:0|intcomma }} - ${{ job.budget_max|floatformat:0|intcomma }}
                        {% endif %}
                    </div>
                    
                    {% if job.duration_weeks %}
                    <div class="text-sm text-gray-500 mb-3">
                        {{ job.duration_weeks }} week{{ job.duration_weeks|pluralize }}
                    </div>
                    {% endif %}
                    
                    <a href="{% url 'jobs:detail' job.id %}" 
                       class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        View Details
                    </a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if page_obj.has_next %}
    <div class="mt-8 text-center">
        <button class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                data-action="click->job-search#loadMore">
            Load More Jobs
        </button>
    </div>
    {% endif %}

{% else %}
    <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No jobs found</h3>
        <p class="mt-1 text-sm text-gray-500">Try adjusting your search criteria or browse all jobs.</p>
        <div class="mt-6">
            <a href="{% url 'jobs:list' %}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                Browse All Jobs
            </a>
        </div>
    </div>
{% endif %}
```

## ✅ Expected Outcome

After completing this exercise, you should have:

1. **Advanced Search Engine**: Sophisticated filtering and sorting capabilities
2. **Real-time Results**: Search updates without page refreshes
3. **Search Suggestions**: Autocomplete functionality for better UX
4. **Multiple Filter Types**: Text, category, skills, budget, location, etc.
5. **Responsive Design**: Works well on all device sizes
6. **Performance Optimized**: Efficient database queries and caching

## 🚀 Extension Challenges

1. **Saved Searches**: Allow users to save and manage search queries
2. **Search Analytics**: Track popular searches and user behavior
3. **Elasticsearch Integration**: Implement full-text search with Elasticsearch
4. **Geolocation Search**: Add map-based location filtering
5. **Machine Learning**: Implement personalized search recommendations

## 🔍 Testing Your Implementation

1. **Test All Filters**: Verify each filter works independently and in combination
2. **Performance Testing**: Check search speed with large datasets
3. **Mobile Testing**: Ensure responsive design works on mobile devices
4. **Edge Cases**: Test with empty results, special characters, etc.
5. **Browser Compatibility**: Test across different browsers

## 💡 Solution Tips

- Use database indexes for frequently searched fields
- Implement proper pagination to handle large result sets
- Add debouncing to prevent excessive API calls
- Use browser history API for back/forward navigation
- Consider implementing search result caching

Excellent work! You've built a sophisticated search system that provides users with powerful tools to find exactly what they're looking for. This exercise demonstrated advanced Django querying, real-time frontend interactions, and creating intuitive user interfaces.

**Next**: [Exercise 3: Notification Center](./exercise-3-notifications.md)
