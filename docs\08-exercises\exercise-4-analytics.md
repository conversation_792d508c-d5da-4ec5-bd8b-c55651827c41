# Exercise 4: Analytics Dashboard 📊

## 🎯 Objective

Create a comprehensive analytics dashboard with interactive charts, key performance indicators (KPIs), and business intelligence features. This exercise focuses on data visualization, performance optimization, and creating actionable insights for platform administrators and users.

## 📋 Prerequisites

- Completed Lessons 1-25 (Full tutorial through Performance Optimization)
- Understanding of data aggregation and complex queries
- Knowledge of JavaScript charting libraries
- Familiarity with caching strategies

## 🎨 What You'll Build

### **Admin Analytics Dashboard**
- Platform-wide KPIs and metrics
- User growth and engagement analytics
- Revenue and transaction analytics
- Job posting and completion trends
- Interactive charts and visualizations

### **User Analytics (Talent/Client)**
- Personal performance metrics
- Earnings/spending analytics
- Project success rates
- Market insights and trends

## 🛠️ Implementation Steps

### **Step 1: Analytics Data Models and Services**

```python
# backend/apps/analytics/models.py
from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import datetime, timedelta
import uuid

User = get_user_model()

class AnalyticsEvent(models.Model):
    class EventType(models.TextChoices):
        PAGE_VIEW = 'page_view', 'Page View'
        USER_REGISTRATION = 'user_registration', 'User Registration'
        JOB_POSTED = 'job_posted', 'Job Posted'
        JOB_APPLICATION = 'job_application', 'Job Application'
        DEAL_CREATED = 'deal_created', 'Deal Created'
        PAYMENT_COMPLETED = 'payment_completed', 'Payment Completed'
        MESSAGE_SENT = 'message_sent', 'Message Sent'
        PROFILE_UPDATED = 'profile_updated', 'Profile Updated'
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    event_type = models.CharField(max_length=50, choices=EventType.choices)
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    
    # Event data
    properties = models.JSONField(default=dict)
    session_id = models.CharField(max_length=255, blank=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    
    # Timestamps
    timestamp = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['event_type', 'timestamp']),
            models.Index(fields=['user', 'timestamp']),
            models.Index(fields=['timestamp']),
        ]

class DailyMetrics(models.Model):
    """Aggregated daily metrics for performance"""
    date = models.DateField(unique=True)
    
    # User metrics
    new_users = models.PositiveIntegerField(default=0)
    active_users = models.PositiveIntegerField(default=0)
    returning_users = models.PositiveIntegerField(default=0)
    
    # Job metrics
    jobs_posted = models.PositiveIntegerField(default=0)
    applications_submitted = models.PositiveIntegerField(default=0)
    deals_created = models.PositiveIntegerField(default=0)
    deals_completed = models.PositiveIntegerField(default=0)
    
    # Financial metrics
    total_revenue = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    total_volume = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    avg_deal_value = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    
    # Engagement metrics
    messages_sent = models.PositiveIntegerField(default=0)
    page_views = models.PositiveIntegerField(default=0)
    session_duration_avg = models.DurationField(null=True, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-date']

# backend/apps/analytics/services.py
from django.db.models import Count, Sum, Avg, Q, F
from django.utils import timezone
from datetime import datetime, timedelta
from django.core.cache import cache
from jobs.models import Job, Deal
from finance.models import Transaction
from chat.models import Message
from .models import AnalyticsEvent, DailyMetrics

class AnalyticsService:
    @staticmethod
    def track_event(event_type, user=None, properties=None, request=None):
        """Track an analytics event"""
        event_data = {
            'event_type': event_type,
            'user': user,
            'properties': properties or {},
        }
        
        if request:
            event_data.update({
                'session_id': request.session.session_key,
                'ip_address': AnalyticsService.get_client_ip(request),
                'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            })
        
        return AnalyticsEvent.objects.create(**event_data)
    
    @staticmethod
    def get_client_ip(request):
        """Get client IP address from request"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
    
    @staticmethod
    def get_platform_overview(days=30):
        """Get platform overview metrics"""
        cache_key = f'platform_overview_{days}'
        cached_data = cache.get(cache_key)
        
        if cached_data:
            return cached_data
        
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=days)
        
        # User metrics
        total_users = User.objects.count()
        new_users = User.objects.filter(
            date_joined__date__gte=start_date
        ).count()
        
        active_users = User.objects.filter(
            last_login__date__gte=start_date
        ).count()
        
        # Job metrics
        total_jobs = Job.objects.count()
        active_jobs = Job.objects.filter(status='published').count()
        completed_deals = Deal.objects.filter(
            status='completed',
            updated_at__date__gte=start_date
        ).count()
        
        # Financial metrics
        revenue_data = Transaction.objects.filter(
            transaction_type='fee',
            status='completed',
            created_at__date__gte=start_date
        ).aggregate(
            total_revenue=Sum('amount'),
            transaction_count=Count('id')
        )
        
        volume_data = Transaction.objects.filter(
            transaction_type='payment',
            status='completed',
            created_at__date__gte=start_date
        ).aggregate(
            total_volume=Sum('amount'),
            avg_deal_value=Avg('amount')
        )
        
        overview = {
            'users': {
                'total': total_users,
                'new': new_users,
                'active': active_users,
                'growth_rate': (new_users / max(total_users - new_users, 1)) * 100
            },
            'jobs': {
                'total': total_jobs,
                'active': active_jobs,
                'completed_deals': completed_deals,
                'completion_rate': (completed_deals / max(total_jobs, 1)) * 100
            },
            'revenue': {
                'total': revenue_data['total_revenue'] or 0,
                'transactions': revenue_data['transaction_count'] or 0,
                'volume': volume_data['total_volume'] or 0,
                'avg_deal_value': volume_data['avg_deal_value'] or 0
            }
        }
        
        cache.set(cache_key, overview, 3600)  # Cache for 1 hour
        return overview
    
    @staticmethod
    def get_user_growth_data(days=90):
        """Get user growth data for charts"""
        cache_key = f'user_growth_{days}'
        cached_data = cache.get(cache_key)
        
        if cached_data:
            return cached_data
        
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=days)
        
        # Daily new users
        daily_users = User.objects.filter(
            date_joined__date__gte=start_date
        ).extra(
            select={'day': 'date(date_joined)'}
        ).values('day').annotate(
            count=Count('id')
        ).order_by('day')
        
        # Cumulative users
        cumulative_data = []
        total = User.objects.filter(date_joined__date__lt=start_date).count()
        
        for entry in daily_users:
            total += entry['count']
            cumulative_data.append({
                'date': entry['day'],
                'cumulative': total,
                'daily': entry['count']
            })
        
        growth_data = {
            'daily': list(daily_users),
            'cumulative': cumulative_data
        }
        
        cache.set(cache_key, growth_data, 1800)  # Cache for 30 minutes
        return growth_data
    
    @staticmethod
    def get_revenue_analytics(days=90):
        """Get revenue analytics data"""
        cache_key = f'revenue_analytics_{days}'
        cached_data = cache.get(cache_key)
        
        if cached_data:
            return cached_data
        
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=days)
        
        # Daily revenue
        daily_revenue = Transaction.objects.filter(
            transaction_type='fee',
            status='completed',
            created_at__date__gte=start_date
        ).extra(
            select={'day': 'date(created_at)'}
        ).values('day').annotate(
            revenue=Sum('amount'),
            transactions=Count('id')
        ).order_by('day')
        
        # Revenue by category
        category_revenue = Transaction.objects.filter(
            transaction_type='fee',
            status='completed',
            created_at__date__gte=start_date
        ).values(
            'deal__job__category__name'
        ).annotate(
            revenue=Sum('amount'),
            deals=Count('deal', distinct=True)
        ).order_by('-revenue')[:10]
        
        # Monthly recurring revenue (MRR) - if applicable
        monthly_data = Transaction.objects.filter(
            transaction_type='fee',
            status='completed',
            created_at__date__gte=start_date
        ).extra(
            select={'month': "DATE_FORMAT(created_at, '%%Y-%%m')"}
        ).values('month').annotate(
            revenue=Sum('amount'),
            transactions=Count('id')
        ).order_by('month')
        
        revenue_data = {
            'daily': list(daily_revenue),
            'by_category': list(category_revenue),
            'monthly': list(monthly_data)
        }
        
        cache.set(cache_key, revenue_data, 1800)
        return revenue_data
    
    @staticmethod
    def get_user_analytics(user, days=90):
        """Get analytics for a specific user"""
        cache_key = f'user_analytics_{user.id}_{days}'
        cached_data = cache.get(cache_key)
        
        if cached_data:
            return cached_data
        
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=days)
        
        if user.role == 'talent':
            analytics = AnalyticsService._get_talent_analytics(user, start_date, end_date)
        elif user.role == 'client':
            analytics = AnalyticsService._get_client_analytics(user, start_date, end_date)
        else:
            analytics = {}
        
        cache.set(cache_key, analytics, 1800)
        return analytics
    
    @staticmethod
    def _get_talent_analytics(user, start_date, end_date):
        """Get analytics for talent users"""
        talent = user.talent
        
        # Earnings data
        earnings = Transaction.objects.filter(
            deal__talent=talent,
            transaction_type='payout',
            status='completed',
            created_at__date__gte=start_date
        ).aggregate(
            total_earnings=Sum('amount'),
            transaction_count=Count('id'),
            avg_earning=Avg('amount')
        )
        
        # Project data
        projects = Deal.objects.filter(
            talent=talent,
            created_at__date__gte=start_date
        ).aggregate(
            total_projects=Count('id'),
            completed_projects=Count('id', filter=Q(status='completed')),
            active_projects=Count('id', filter=Q(status='active'))
        )
        
        # Success rate
        success_rate = 0
        if projects['total_projects'] > 0:
            success_rate = (projects['completed_projects'] / projects['total_projects']) * 100
        
        # Daily earnings
        daily_earnings = Transaction.objects.filter(
            deal__talent=talent,
            transaction_type='payout',
            status='completed',
            created_at__date__gte=start_date
        ).extra(
            select={'day': 'date(created_at)'}
        ).values('day').annotate(
            earnings=Sum('amount')
        ).order_by('day')
        
        return {
            'earnings': earnings,
            'projects': projects,
            'success_rate': success_rate,
            'daily_earnings': list(daily_earnings),
            'avg_project_value': earnings['total_earnings'] / max(projects['completed_projects'], 1) if earnings['total_earnings'] else 0
        }
    
    @staticmethod
    def _get_client_analytics(user, start_date, end_date):
        """Get analytics for client users"""
        client = user.client
        
        # Spending data
        spending = Transaction.objects.filter(
            deal__client=client,
            transaction_type='payment',
            status='completed',
            created_at__date__gte=start_date
        ).aggregate(
            total_spending=Sum('amount'),
            transaction_count=Count('id'),
            avg_spending=Avg('amount')
        )
        
        # Job data
        jobs = Job.objects.filter(
            client=client,
            created_at__date__gte=start_date
        ).aggregate(
            total_jobs=Count('id'),
            active_jobs=Count('id', filter=Q(status='published')),
            completed_jobs=Count('id', filter=Q(status='completed'))
        )
        
        # Application data
        applications = Deal.objects.filter(
            job__client=client,
            created_at__date__gte=start_date
        ).aggregate(
            total_applications=Count('id'),
            hired_applications=Count('id', filter=Q(status__in=['active', 'completed']))
        )
        
        # Hire rate
        hire_rate = 0
        if applications['total_applications'] > 0:
            hire_rate = (applications['hired_applications'] / applications['total_applications']) * 100
        
        # Daily spending
        daily_spending = Transaction.objects.filter(
            deal__client=client,
            transaction_type='payment',
            status='completed',
            created_at__date__gte=start_date
        ).extra(
            select={'day': 'date(created_at)'}
        ).values('day').annotate(
            spending=Sum('amount')
        ).order_by('day')
        
        return {
            'spending': spending,
            'jobs': jobs,
            'applications': applications,
            'hire_rate': hire_rate,
            'daily_spending': list(daily_spending),
            'avg_job_value': spending['total_spending'] / max(jobs['completed_jobs'], 1) if spending['total_spending'] else 0
        }
    
    @staticmethod
    def generate_daily_metrics():
        """Generate daily metrics (run as scheduled task)"""
        yesterday = timezone.now().date() - timedelta(days=1)
        
        # Check if metrics already exist
        if DailyMetrics.objects.filter(date=yesterday).exists():
            return
        
        # Calculate metrics for yesterday
        start_datetime = datetime.combine(yesterday, datetime.min.time())
        end_datetime = datetime.combine(yesterday, datetime.max.time())
        
        # User metrics
        new_users = User.objects.filter(
            date_joined__range=[start_datetime, end_datetime]
        ).count()
        
        active_users = User.objects.filter(
            last_login__range=[start_datetime, end_datetime]
        ).count()
        
        # Job metrics
        jobs_posted = Job.objects.filter(
            created_at__range=[start_datetime, end_datetime]
        ).count()
        
        applications_submitted = Deal.objects.filter(
            created_at__range=[start_datetime, end_datetime]
        ).count()
        
        deals_completed = Deal.objects.filter(
            status='completed',
            updated_at__range=[start_datetime, end_datetime]
        ).count()
        
        # Financial metrics
        revenue_data = Transaction.objects.filter(
            transaction_type='fee',
            status='completed',
            created_at__range=[start_datetime, end_datetime]
        ).aggregate(
            total_revenue=Sum('amount') or 0
        )
        
        volume_data = Transaction.objects.filter(
            transaction_type='payment',
            status='completed',
            created_at__range=[start_datetime, end_datetime]
        ).aggregate(
            total_volume=Sum('amount') or 0,
            avg_deal_value=Avg('amount') or 0
        )
        
        # Engagement metrics
        messages_sent = Message.objects.filter(
            created_at__range=[start_datetime, end_datetime]
        ).count()
        
        page_views = AnalyticsEvent.objects.filter(
            event_type='page_view',
            timestamp__range=[start_datetime, end_datetime]
        ).count()
        
        # Create daily metrics record
        DailyMetrics.objects.create(
            date=yesterday,
            new_users=new_users,
            active_users=active_users,
            jobs_posted=jobs_posted,
            applications_submitted=applications_submitted,
            deals_completed=deals_completed,
            total_revenue=revenue_data['total_revenue'],
            total_volume=volume_data['total_volume'],
            avg_deal_value=volume_data['avg_deal_value'],
            messages_sent=messages_sent,
            page_views=page_views
        )
```

### **Step 2: Analytics Views and API Endpoints**

```python
# backend/apps/analytics/views.py
from django.shortcuts import render
from django.contrib.admin.views.decorators import staff_member_required
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.utils.decorators import method_decorator
from django.views.generic import TemplateView
from .services import AnalyticsService
import json

@method_decorator(staff_member_required, name='dispatch')
class AdminAnalyticsDashboard(TemplateView):
    template_name = 'analytics/admin_dashboard.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get overview data
        context['overview'] = AnalyticsService.get_platform_overview()
        
        # Get recent metrics
        from .models import DailyMetrics
        context['recent_metrics'] = DailyMetrics.objects.all()[:30]
        
        return context

@login_required
def user_analytics_dashboard(request):
    """Analytics dashboard for individual users"""
    analytics_data = AnalyticsService.get_user_analytics(request.user)
    
    context = {
        'analytics': analytics_data,
        'user_role': request.user.role
    }
    
    return render(request, 'analytics/user_dashboard.html', context)

# API endpoints for AJAX requests
@staff_member_required
def api_platform_overview(request):
    """API endpoint for platform overview data"""
    days = int(request.GET.get('days', 30))
    data = AnalyticsService.get_platform_overview(days)
    return JsonResponse(data)

@staff_member_required
def api_user_growth(request):
    """API endpoint for user growth data"""
    days = int(request.GET.get('days', 90))
    data = AnalyticsService.get_user_growth_data(days)
    return JsonResponse(data)

@staff_member_required
def api_revenue_analytics(request):
    """API endpoint for revenue analytics"""
    days = int(request.GET.get('days', 90))
    data = AnalyticsService.get_revenue_analytics(days)
    return JsonResponse(data)

@login_required
def api_user_analytics(request):
    """API endpoint for user-specific analytics"""
    days = int(request.GET.get('days', 90))
    data = AnalyticsService.get_user_analytics(request.user, days)
    return JsonResponse(data)

# Middleware to track page views
class AnalyticsMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)
        
        # Track page view for GET requests
        if request.method == 'GET' and response.status_code == 200:
            # Skip admin and API endpoints
            if not request.path.startswith('/admin/') and not request.path.startswith('/api/'):
                AnalyticsService.track_event(
                    'page_view',
                    user=request.user if request.user.is_authenticated else None,
                    properties={
                        'path': request.path,
                        'referrer': request.META.get('HTTP_REFERER', ''),
                    },
                    request=request
                )
        
        return response
```

### **Step 3: Interactive Dashboard Templates**

```html
<!-- backend/templates/analytics/admin_dashboard.html -->
{% extends 'admin/base.html' %}
{% load humanize %}

{% block title %}Analytics Dashboard{% endblock %}

{% block content %}
<div class="analytics-dashboard" 
     data-controller="analytics-dashboard"
     data-analytics-dashboard-overview-url-value="{% url 'analytics:api_platform_overview' %}"
     data-analytics-dashboard-user-growth-url-value="{% url 'analytics:api_user_growth' %}"
     data-analytics-dashboard-revenue-url-value="{% url 'analytics:api_revenue_analytics' %}">
    
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Analytics Dashboard</h1>
        <p class="text-gray-600 mt-2">Platform performance and insights</p>
        
        <!-- Time Range Selector -->
        <div class="mt-4">
            <select data-analytics-dashboard-target="timeRange" 
                    data-action="change->analytics-dashboard#updateTimeRange"
                    class="rounded-md border-gray-300 focus:border-blue-500 focus:ring-blue-500">
                <option value="7">Last 7 days</option>
                <option value="30" selected>Last 30 days</option>
                <option value="90">Last 90 days</option>
                <option value="365">Last year</option>
            </select>
        </div>
    </div>

    <!-- KPI Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"/>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Users</dt>
                        <dd class="text-lg font-medium text-gray-900" data-analytics-dashboard-target="totalUsers">
                            {{ overview.users.total|intcomma }}
                        </dd>
                    </dl>
                </div>
            </div>
            <div class="mt-4">
                <div class="flex items-center text-sm">
                    <span class="text-green-600 font-medium">+{{ overview.users.new|intcomma }}</span>
                    <span class="text-gray-500 ml-1">new this period</span>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Revenue</dt>
                        <dd class="text-lg font-medium text-gray-900" data-analytics-dashboard-target="totalRevenue">
                            ${{ overview.revenue.total|floatformat:0|intcomma }}
                        </dd>
                    </dl>
                </div>
            </div>
            <div class="mt-4">
                <div class="flex items-center text-sm">
                    <span class="text-gray-500">{{ overview.revenue.transactions|intcomma }} transactions</span>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Active Jobs</dt>
                        <dd class="text-lg font-medium text-gray-900" data-analytics-dashboard-target="activeJobs">
                            {{ overview.jobs.active|intcomma }}
                        </dd>
                    </dl>
                </div>
            </div>
            <div class="mt-4">
                <div class="flex items-center text-sm">
                    <span class="text-gray-500">{{ overview.jobs.completion_rate|floatformat:1 }}% completion rate</span>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"/>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Avg Deal Value</dt>
                        <dd class="text-lg font-medium text-gray-900" data-analytics-dashboard-target="avgDealValue">
                            ${{ overview.revenue.avg_deal_value|floatformat:0|intcomma }}
                        </dd>
                    </dl>
                </div>
            </div>
            <div class="mt-4">
                <div class="flex items-center text-sm">
                    <span class="text-gray-500">${{ overview.revenue.volume|floatformat:0|intcomma }} total volume</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- User Growth Chart -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">User Growth</h3>
                <div class="flex space-x-2">
                    <button type="button" 
                            class="px-3 py-1 text-sm bg-blue-100 text-blue-800 rounded"
                            data-action="click->analytics-dashboard#showDailyGrowth">
                        Daily
                    </button>
                    <button type="button" 
                            class="px-3 py-1 text-sm bg-gray-100 text-gray-800 rounded"
                            data-action="click->analytics-dashboard#showCumulativeGrowth">
                        Cumulative
                    </button>
                </div>
            </div>
            <div class="h-64">
                <canvas data-analytics-dashboard-target="userGrowthChart"></canvas>
            </div>
        </div>

        <!-- Revenue Chart -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Revenue Trends</h3>
                <select data-analytics-dashboard-target="revenueChartType"
                        data-action="change->analytics-dashboard#updateRevenueChart"
                        class="text-sm border-gray-300 rounded">
                    <option value="daily">Daily</option>
                    <option value="monthly">Monthly</option>
                    <option value="category">By Category</option>
                </select>
            </div>
            <div class="h-64">
                <canvas data-analytics-dashboard-target="revenueChart"></canvas>
            </div>
        </div>
    </div>

    <!-- Detailed Tables -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Top Categories -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Top Categories</h3>
            </div>
            <div class="p-6">
                <div class="space-y-4" data-analytics-dashboard-target="topCategories">
                    <!-- Will be populated by JavaScript -->
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Recent Metrics</h3>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    {% for metric in recent_metrics|slice:":7" %}
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-900">{{ metric.date|date:"M d" }}</p>
                            <p class="text-sm text-gray-500">{{ metric.new_users }} new users</p>
                        </div>
                        <div class="text-right">
                            <p class="text-sm font-medium text-gray-900">${{ metric.total_revenue|floatformat:0|intcomma }}</p>
                            <p class="text-sm text-gray-500">{{ metric.deals_completed }} deals</p>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div data-analytics-dashboard-target="loading" 
         class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg p-6 flex items-center">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3"></div>
            <span class="text-gray-900">Loading analytics...</span>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}
```

### **Step 4: Analytics Dashboard Stimulus Controller**

```javascript
// frontend/src/controllers/analytics_dashboard_controller.js
import { Controller } from "@hotwired/stimulus"
import Chart from 'chart.js/auto'

export default class extends Controller {
  static targets = [
    "timeRange", "loading", "userGrowthChart", "revenueChart", 
    "revenueChartType", "topCategories", "totalUsers", "totalRevenue", 
    "activeJobs", "avgDealValue"
  ]
  
  static values = {
    overviewUrl: String,
    userGrowthUrl: String,
    revenueUrl: String
  }
  
  connect() {
    this.userGrowthChartInstance = null
    this.revenueChartInstance = null
    this.currentTimeRange = 30
    this.userGrowthMode = 'daily' // 'daily' or 'cumulative'
    
    this.loadInitialData()
  }
  
  disconnect() {
    if (this.userGrowthChartInstance) {
      this.userGrowthChartInstance.destroy()
    }
    if (this.revenueChartInstance) {
      this.revenueChartInstance.destroy()
    }
  }
  
  // Load initial dashboard data
  async loadInitialData() {
    this.showLoading()
    
    try {
      await Promise.all([
        this.loadOverviewData(),
        this.loadUserGrowthData(),
        this.loadRevenueData()
      ])
    } catch (error) {
      console.error('Error loading dashboard data:', error)
      this.showError('Failed to load dashboard data')
    } finally {
      this.hideLoading()
    }
  }
  
  // Update time range
  async updateTimeRange(event) {
    this.currentTimeRange = parseInt(event.target.value)
    await this.loadInitialData()
  }
  
  // Load overview data
  async loadOverviewData() {
    const response = await fetch(`${this.overviewUrlValue}?days=${this.currentTimeRange}`)
    const data = await response.json()
    
    // Update KPI cards
    this.totalUsersTarget.textContent = this.formatNumber(data.users.total)
    this.totalRevenueTarget.textContent = `$${this.formatNumber(data.revenue.total)}`
    this.activeJobsTarget.textContent = this.formatNumber(data.jobs.active)
    this.avgDealValueTarget.textContent = `$${this.formatNumber(data.revenue.avg_deal_value)}`
  }
  
  // Load user growth data
  async loadUserGrowthData() {
    const response = await fetch(`${this.userGrowthUrlValue}?days=${this.currentTimeRange}`)
    const data = await response.json()
    
    this.userGrowthData = data
    this.renderUserGrowthChart()
  }
  
  // Load revenue data
  async loadRevenueData() {
    const response = await fetch(`${this.revenueUrlValue}?days=${this.currentTimeRange}`)
    const data = await response.json()
    
    this.revenueData = data
    this.renderRevenueChart()
    this.renderTopCategories(data.by_category)
  }
  
  // Render user growth chart
  renderUserGrowthChart() {
    const ctx = this.userGrowthChartTarget.getContext('2d')
    
    if (this.userGrowthChartInstance) {
      this.userGrowthChartInstance.destroy()
    }
    
    const dataset = this.userGrowthMode === 'daily' 
      ? this.userGrowthData.daily 
      : this.userGrowthData.cumulative
    
    const labels = dataset.map(item => {
      const date = new Date(item.day || item.date)
      return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
    })
    
    const values = dataset.map(item => 
      this.userGrowthMode === 'daily' ? item.count : item.cumulative
    )
    
    this.userGrowthChartInstance = new Chart(ctx, {
      type: 'line',
      data: {
        labels: labels,
        datasets: [{
          label: this.userGrowthMode === 'daily' ? 'New Users' : 'Total Users',
          data: values,
          borderColor: 'rgb(59, 130, 246)',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          fill: true,
          tension: 0.4
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              callback: function(value) {
                return value.toLocaleString()
              }
            }
          }
        },
        interaction: {
          intersect: false,
          mode: 'index'
        }
      }
    })
  }
  
  // Render revenue chart
  renderRevenueChart() {
    const ctx = this.revenueChartTarget.getContext('2d')
    
    if (this.revenueChartInstance) {
      this.revenueChartInstance.destroy()
    }
    
    const chartType = this.revenueChartTypeTarget.value
    let chartData, chartConfig
    
    if (chartType === 'category') {
      chartData = this.revenueData.by_category.slice(0, 10)
      chartConfig = {
        type: 'doughnut',
        data: {
          labels: chartData.map(item => item.deal__job__category__name || 'Other'),
          datasets: [{
            data: chartData.map(item => item.revenue),
            backgroundColor: [
              '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6',
              '#06B6D4', '#84CC16', '#F97316', '#EC4899', '#6B7280'
            ]
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'right'
            }
          }
        }
      }
    } else {
      const dataset = chartType === 'daily' 
        ? this.revenueData.daily 
        : this.revenueData.monthly
      
      const labels = dataset.map(item => {
        if (chartType === 'daily') {
          const date = new Date(item.day)
          return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
        } else {
          return item.month
        }
      })
      
      chartConfig = {
        type: 'bar',
        data: {
          labels: labels,
          datasets: [{
            label: 'Revenue',
            data: dataset.map(item => item.revenue),
            backgroundColor: 'rgba(16, 185, 129, 0.8)',
            borderColor: 'rgb(16, 185, 129)',
            borderWidth: 1
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              ticks: {
                callback: function(value) {
                  return '$' + value.toLocaleString()
                }
              }
            }
          }
        }
      }
    }
    
    this.revenueChartInstance = new Chart(ctx, chartConfig)
  }
  
  // Render top categories table
  renderTopCategories(categories) {
    const html = categories.slice(0, 5).map(category => `
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm font-medium text-gray-900">${category.deal__job__category__name || 'Other'}</p>
          <p class="text-sm text-gray-500">${category.deals} deals</p>
        </div>
        <div class="text-right">
          <p class="text-sm font-medium text-gray-900">$${this.formatNumber(category.revenue)}</p>
        </div>
      </div>
    `).join('')
    
    this.topCategoriesTarget.innerHTML = html
  }
  
  // Chart mode toggles
  showDailyGrowth() {
    this.userGrowthMode = 'daily'
    this.renderUserGrowthChart()
    this.updateGrowthButtons('daily')
  }
  
  showCumulativeGrowth() {
    this.userGrowthMode = 'cumulative'
    this.renderUserGrowthChart()
    this.updateGrowthButtons('cumulative')
  }
  
  updateGrowthButtons(activeMode) {
    const buttons = this.element.querySelectorAll('[data-action*="Growth"]')
    buttons.forEach(button => {
      button.classList.remove('bg-blue-100', 'text-blue-800')
      button.classList.add('bg-gray-100', 'text-gray-800')
    })
    
    const activeButton = this.element.querySelector(`[data-action*="${activeMode === 'daily' ? 'Daily' : 'Cumulative'}Growth"]`)
    if (activeButton) {
      activeButton.classList.remove('bg-gray-100', 'text-gray-800')
      activeButton.classList.add('bg-blue-100', 'text-blue-800')
    }
  }
  
  updateRevenueChart() {
    this.renderRevenueChart()
  }
  
  // Utility methods
  formatNumber(num) {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M'
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K'
    }
    return num.toLocaleString()
  }
  
  showLoading() {
    this.loadingTarget.classList.remove('hidden')
  }
  
  hideLoading() {
    this.loadingTarget.classList.add('hidden')
  }
  
  showError(message) {
    // Create and show error toast
    const toast = document.createElement('div')
    toast.className = 'fixed top-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded z-50'
    toast.textContent = message
    
    document.body.appendChild(toast)
    
    setTimeout(() => {
      toast.remove()
    }, 5000)
  }
}
```

## ✅ Expected Outcome

After completing this exercise, you should have:

1. **Comprehensive Analytics Dashboard**: Interactive charts and KPIs for platform insights
2. **Real-time Data Visualization**: Dynamic charts that update based on time ranges
3. **User-specific Analytics**: Personalized dashboards for talents and clients
4. **Performance Optimization**: Efficient data aggregation and caching
5. **Business Intelligence**: Actionable insights for decision making

## 🚀 Extension Challenges

1. **Predictive Analytics**: Implement machine learning models for forecasting
2. **A/B Testing Framework**: Create system for testing feature variations
3. **Custom Report Builder**: Allow users to create custom analytics reports
4. **Real-time Dashboards**: Use WebSockets for live updating dashboards
5. **Export Functionality**: Add PDF/Excel export capabilities for reports

## 🔍 Testing Your Implementation

1. **Data Accuracy**: Verify analytics calculations match actual data
2. **Performance Testing**: Test with large datasets and multiple concurrent users
3. **Chart Responsiveness**: Ensure charts work on different screen sizes
4. **Cache Effectiveness**: Verify caching improves performance
5. **User Experience**: Test dashboard usability and loading times

## 💡 Solution Tips

- Use database aggregation functions for efficient calculations
- Implement proper caching strategies for expensive queries
- Consider using background tasks for heavy analytics processing
- Use chart.js or similar libraries for interactive visualizations
- Implement proper error handling and loading states

Fantastic work! You've built a sophisticated analytics dashboard that provides valuable insights into platform performance and user behavior. This exercise demonstrated advanced data visualization, performance optimization, and creating business intelligence tools.

You've now completed all four practical exercises, gaining hands-on experience with:
- Custom dashboards and user interfaces
- Advanced search and filtering systems
- Real-time notifications and WebSocket integration
- Data analytics and visualization

These exercises have reinforced the concepts from the tutorial and provided practical experience building complex, production-ready features. Well done! 🎉
