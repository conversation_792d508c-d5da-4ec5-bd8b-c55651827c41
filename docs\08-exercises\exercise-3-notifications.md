# Exercise 3: Notification Center 🔔

## 🎯 Objective

Build a comprehensive notification system with real-time updates, user preferences, multiple delivery channels, and an intuitive notification center interface. This exercise focuses on WebSocket integration, user experience design, and managing complex state.

## 📋 Prerequisites

- Completed Lessons 1-18 (Setup through Chat System)
- Understanding of WebSockets and real-time communication
- Knowledge of Django Channels and Redis
- Familiarity with JavaScript event handling

## 🎨 What You'll Build

### **Notification Center Features**
- Real-time notification dropdown
- Mark as read/unread functionality
- Notification categories and filtering
- Bulk actions (mark all as read, delete)
- Notification preferences management
- Push notification support

### **Real-time Updates**
- WebSocket connection for live notifications
- Toast notifications for immediate feedback
- Badge counters for unread notifications
- Sound notifications (optional)
- Browser notification API integration

## 🛠️ Implementation Steps

### **Step 1: Enhanced Notification Models**

```python
# backend/apps/notifications/models.py
from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
import uuid

User = get_user_model()

class NotificationCategory(models.Model):
    name = models.CharField(max_length=50, unique=True)
    display_name = models.CharField(max_length=100)
    icon = models.CharField(max_length=50)  # CSS class or icon name
    color = models.CharField(max_length=20, default='blue')
    is_active = models.BooleanField(default=True)
    
    class Meta:
        verbose_name_plural = "Notification Categories"
    
    def __str__(self):
        return self.display_name

class Notification(models.Model):
    class Priority(models.TextChoices):
        LOW = 'low', 'Low'
        NORMAL = 'normal', 'Normal'
        HIGH = 'high', 'High'
        URGENT = 'urgent', 'Urgent'
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    recipient = models.ForeignKey(User, on_delete=models.CASCADE, related_name='notifications')
    category = models.ForeignKey(NotificationCategory, on_delete=models.CASCADE)
    
    # Content
    title = models.CharField(max_length=255)
    message = models.TextField()
    action_url = models.URLField(blank=True)
    action_text = models.CharField(max_length=50, blank=True)
    
    # Metadata
    priority = models.CharField(max_length=10, choices=Priority.choices, default=Priority.NORMAL)
    data = models.JSONField(default=dict)  # Additional context data
    
    # Status
    is_read = models.BooleanField(default=False)
    is_archived = models.BooleanField(default=False)
    read_at = models.DateTimeField(null=True, blank=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['recipient', 'is_read', 'created_at']),
            models.Index(fields=['category', 'created_at']),
            models.Index(fields=['priority', 'created_at']),
        ]
    
    def mark_as_read(self):
        if not self.is_read:
            self.is_read = True
            self.read_at = timezone.now()
            self.save(update_fields=['is_read', 'read_at'])
    
    def mark_as_unread(self):
        if self.is_read:
            self.is_read = False
            self.read_at = None
            self.save(update_fields=['is_read', 'read_at'])

class NotificationPreference(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    category = models.ForeignKey(NotificationCategory, on_delete=models.CASCADE)
    
    # Delivery preferences
    email_enabled = models.BooleanField(default=True)
    push_enabled = models.BooleanField(default=True)
    in_app_enabled = models.BooleanField(default=True)
    
    # Frequency settings
    email_frequency = models.CharField(
        max_length=20,
        choices=[
            ('immediate', 'Immediate'),
            ('daily', 'Daily Digest'),
            ('weekly', 'Weekly Digest'),
            ('never', 'Never')
        ],
        default='immediate'
    )
    
    class Meta:
        unique_together = ['user', 'category']

# Notification service
class NotificationService:
    @staticmethod
    def create_notification(recipient, category_name, title, message, **kwargs):
        """Create a new notification"""
        try:
            category = NotificationCategory.objects.get(name=category_name)
        except NotificationCategory.DoesNotExist:
            category = NotificationCategory.objects.create(
                name=category_name,
                display_name=category_name.replace('_', ' ').title()
            )
        
        notification = Notification.objects.create(
            recipient=recipient,
            category=category,
            title=title,
            message=message,
            **kwargs
        )
        
        # Send real-time notification
        from .consumers import send_notification_to_user
        send_notification_to_user(recipient.id, notification)
        
        return notification
    
    @staticmethod
    def mark_all_as_read(user, category=None):
        """Mark all notifications as read for a user"""
        queryset = user.notifications.filter(is_read=False)
        if category:
            queryset = queryset.filter(category=category)
        
        queryset.update(is_read=True, read_at=timezone.now())
    
    @staticmethod
    def get_unread_count(user, category=None):
        """Get unread notification count"""
        queryset = user.notifications.filter(is_read=False, is_archived=False)
        if category:
            queryset = queryset.filter(category=category)
        
        return queryset.count()
```

### **Step 2: WebSocket Consumer for Real-time Notifications**

```python
# backend/apps/notifications/consumers.py
import json
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.contrib.auth import get_user_model
from .models import Notification

User = get_user_model()

class NotificationConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        if self.scope["user"].is_anonymous:
            await self.close()
            return
        
        self.user_id = self.scope["user"].id
        self.group_name = f"notifications_{self.user_id}"
        
        # Join notification group
        await self.channel_layer.group_add(
            self.group_name,
            self.channel_name
        )
        
        await self.accept()
        
        # Send initial unread count
        unread_count = await self.get_unread_count()
        await self.send(text_data=json.dumps({
            'type': 'unread_count',
            'count': unread_count
        }))
    
    async def disconnect(self, close_code):
        if hasattr(self, 'group_name'):
            await self.channel_layer.group_discard(
                self.group_name,
                self.channel_name
            )
    
    async def receive(self, text_data):
        data = json.loads(text_data)
        message_type = data.get('type')
        
        if message_type == 'mark_as_read':
            await self.mark_notification_as_read(data.get('notification_id'))
        elif message_type == 'mark_all_as_read':
            await self.mark_all_as_read()
        elif message_type == 'get_notifications':
            await self.send_notifications(data.get('page', 1))
    
    # WebSocket message handlers
    async def notification_message(self, event):
        """Send notification to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'new_notification',
            'notification': event['notification']
        }))
    
    async def unread_count_update(self, event):
        """Send updated unread count"""
        await self.send(text_data=json.dumps({
            'type': 'unread_count',
            'count': event['count']
        }))
    
    # Database operations
    @database_sync_to_async
    def get_unread_count(self):
        return self.scope["user"].notifications.filter(
            is_read=False, 
            is_archived=False
        ).count()
    
    @database_sync_to_async
    def mark_notification_as_read(self, notification_id):
        try:
            notification = self.scope["user"].notifications.get(id=notification_id)
            notification.mark_as_read()
            return True
        except Notification.DoesNotExist:
            return False
    
    @database_sync_to_async
    def mark_all_as_read(self):
        self.scope["user"].notifications.filter(is_read=False).update(
            is_read=True,
            read_at=timezone.now()
        )
    
    @database_sync_to_async
    def get_notifications(self, page=1, per_page=20):
        from django.core.paginator import Paginator
        
        notifications = self.scope["user"].notifications.filter(
            is_archived=False
        ).select_related('category')
        
        paginator = Paginator(notifications, per_page)
        page_obj = paginator.get_page(page)
        
        return {
            'notifications': [
                {
                    'id': str(n.id),
                    'title': n.title,
                    'message': n.message,
                    'category': {
                        'name': n.category.name,
                        'display_name': n.category.display_name,
                        'icon': n.category.icon,
                        'color': n.category.color
                    },
                    'priority': n.priority,
                    'is_read': n.is_read,
                    'action_url': n.action_url,
                    'action_text': n.action_text,
                    'created_at': n.created_at.isoformat(),
                    'read_at': n.read_at.isoformat() if n.read_at else None
                }
                for n in page_obj
            ],
            'has_next': page_obj.has_next(),
            'has_previous': page_obj.has_previous(),
            'total_count': paginator.count
        }
    
    async def send_notifications(self, page):
        """Send paginated notifications to client"""
        data = await self.get_notifications(page)
        await self.send(text_data=json.dumps({
            'type': 'notifications_list',
            **data
        }))

# Helper function to send notifications
def send_notification_to_user(user_id, notification):
    """Send notification to user's WebSocket group"""
    from channels.layers import get_channel_layer
    from asgiref.sync import async_to_sync
    
    channel_layer = get_channel_layer()
    group_name = f"notifications_{user_id}"
    
    notification_data = {
        'id': str(notification.id),
        'title': notification.title,
        'message': notification.message,
        'category': {
            'name': notification.category.name,
            'display_name': notification.category.display_name,
            'icon': notification.category.icon,
            'color': notification.category.color
        },
        'priority': notification.priority,
        'is_read': notification.is_read,
        'action_url': notification.action_url,
        'action_text': notification.action_text,
        'created_at': notification.created_at.isoformat()
    }
    
    async_to_sync(channel_layer.group_send)(
        group_name,
        {
            'type': 'notification_message',
            'notification': notification_data
        }
    )
    
    # Also send updated unread count
    from .models import NotificationService
    unread_count = NotificationService.get_unread_count(notification.recipient)
    
    async_to_sync(channel_layer.group_send)(
        group_name,
        {
            'type': 'unread_count_update',
            'count': unread_count
        }
    )
```

### **Step 3: Notification Center Views**

```python
# backend/apps/notifications/views.py
from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.views.decorators.http import require_POST
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.views.generic import ListView
from .models import Notification, NotificationCategory, NotificationService
import json

@login_required
def notification_center(request):
    """Main notification center view"""
    categories = NotificationCategory.objects.filter(is_active=True)
    
    # Get filter parameters
    category_filter = request.GET.get('category')
    status_filter = request.GET.get('status', 'all')  # all, unread, read
    
    # Build queryset
    notifications = request.user.notifications.filter(is_archived=False)
    
    if category_filter:
        notifications = notifications.filter(category__name=category_filter)
    
    if status_filter == 'unread':
        notifications = notifications.filter(is_read=False)
    elif status_filter == 'read':
        notifications = notifications.filter(is_read=True)
    
    # Pagination
    paginator = Paginator(notifications.select_related('category'), 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Statistics
    stats = {
        'total': request.user.notifications.filter(is_archived=False).count(),
        'unread': request.user.notifications.filter(is_read=False, is_archived=False).count(),
        'read': request.user.notifications.filter(is_read=True, is_archived=False).count(),
    }
    
    context = {
        'notifications': page_obj,
        'categories': categories,
        'stats': stats,
        'current_category': category_filter,
        'current_status': status_filter,
    }
    
    return render(request, 'notifications/center.html', context)

@login_required
def notification_dropdown(request):
    """AJAX view for notification dropdown"""
    notifications = request.user.notifications.filter(
        is_archived=False
    ).select_related('category')[:10]
    
    unread_count = request.user.notifications.filter(
        is_read=False, 
        is_archived=False
    ).count()
    
    notifications_data = [
        {
            'id': str(n.id),
            'title': n.title,
            'message': n.message,
            'category': {
                'name': n.category.name,
                'display_name': n.category.display_name,
                'icon': n.category.icon,
                'color': n.category.color
            },
            'priority': n.priority,
            'is_read': n.is_read,
            'action_url': n.action_url,
            'action_text': n.action_text,
            'created_at': n.created_at.isoformat(),
            'time_ago': n.created_at
        }
        for n in notifications
    ]
    
    return JsonResponse({
        'notifications': notifications_data,
        'unread_count': unread_count,
        'has_more': request.user.notifications.filter(is_archived=False).count() > 10
    })

@require_POST
@login_required
def mark_as_read(request, notification_id):
    """Mark single notification as read"""
    notification = get_object_or_404(
        request.user.notifications, 
        id=notification_id
    )
    
    notification.mark_as_read()
    
    return JsonResponse({
        'success': True,
        'unread_count': NotificationService.get_unread_count(request.user)
    })

@require_POST
@login_required
def mark_as_unread(request, notification_id):
    """Mark single notification as unread"""
    notification = get_object_or_404(
        request.user.notifications, 
        id=notification_id
    )
    
    notification.mark_as_unread()
    
    return JsonResponse({
        'success': True,
        'unread_count': NotificationService.get_unread_count(request.user)
    })

@require_POST
@login_required
def mark_all_as_read(request):
    """Mark all notifications as read"""
    category_name = request.POST.get('category')
    category = None
    
    if category_name:
        try:
            category = NotificationCategory.objects.get(name=category_name)
        except NotificationCategory.DoesNotExist:
            pass
    
    NotificationService.mark_all_as_read(request.user, category)
    
    return JsonResponse({
        'success': True,
        'unread_count': NotificationService.get_unread_count(request.user)
    })

@require_POST
@login_required
def bulk_action(request):
    """Handle bulk actions on notifications"""
    data = json.loads(request.body)
    action = data.get('action')
    notification_ids = data.get('notification_ids', [])
    
    notifications = request.user.notifications.filter(
        id__in=notification_ids
    )
    
    if action == 'mark_read':
        notifications.update(is_read=True, read_at=timezone.now())
    elif action == 'mark_unread':
        notifications.update(is_read=False, read_at=None)
    elif action == 'archive':
        notifications.update(is_archived=True)
    elif action == 'delete':
        notifications.delete()
    
    return JsonResponse({
        'success': True,
        'unread_count': NotificationService.get_unread_count(request.user)
    })
```

### **Step 4: Notification Center Frontend**

```html
<!-- backend/templates/notifications/center.html -->
{% extends 'base.html' %}
{% load humanize %}

{% block title %}Notification Center - Crelancer{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"
     data-controller="notification-center"
     data-notification-center-ws-url-value="ws://{{ request.get_host }}/ws/notifications/"
     data-notification-center-mark-read-url-value="{% url 'notifications:mark_as_read' '0' %}"
     data-notification-center-bulk-action-url-value="{% url 'notifications:bulk_action' %}">
    
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Notification Center</h1>
        <p class="text-gray-600 mt-2">Manage your notifications and preferences</p>
    </div>

    <!-- Stats and Filters -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <!-- Stats -->
            <div class="flex space-x-6 mb-4 lg:mb-0">
                <div class="text-center">
                    <div class="text-2xl font-bold text-gray-900">{{ stats.total }}</div>
                    <div class="text-sm text-gray-500">Total</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600">{{ stats.unread }}</div>
                    <div class="text-sm text-gray-500">Unread</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600">{{ stats.read }}</div>
                    <div class="text-sm text-gray-500">Read</div>
                </div>
            </div>

            <!-- Filters -->
            <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4">
                <select name="category" 
                        class="rounded-md border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                        data-action="change->notification-center#filterByCategory">
                    <option value="">All Categories</option>
                    {% for category in categories %}
                    <option value="{{ category.name }}" 
                            {% if current_category == category.name %}selected{% endif %}>
                        {{ category.display_name }}
                    </option>
                    {% endfor %}
                </select>

                <select name="status" 
                        class="rounded-md border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                        data-action="change->notification-center#filterByStatus">
                    <option value="all" {% if current_status == 'all' %}selected{% endif %}>All</option>
                    <option value="unread" {% if current_status == 'unread' %}selected{% endif %}>Unread</option>
                    <option value="read" {% if current_status == 'read' %}selected{% endif %}>Read</option>
                </select>

                <button type="button" 
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                        data-action="click->notification-center#markAllAsRead">
                    Mark All as Read
                </button>
            </div>
        </div>

        <!-- Bulk Actions -->
        <div class="mt-4 flex items-center justify-between border-t border-gray-200 pt-4"
             data-notification-center-target="bulkActions"
             style="display: none;">
            <div class="flex items-center">
                <input type="checkbox" 
                       class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                       data-notification-center-target="selectAll"
                       data-action="change->notification-center#toggleSelectAll">
                <span class="ml-2 text-sm text-gray-700">
                    <span data-notification-center-target="selectedCount">0</span> selected
                </span>
            </div>
            
            <div class="flex space-x-2">
                <button type="button" 
                        class="inline-flex items-center px-3 py-1 border border-gray-300 text-sm font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                        data-action="click->notification-center#bulkMarkRead">
                    Mark Read
                </button>
                <button type="button" 
                        class="inline-flex items-center px-3 py-1 border border-gray-300 text-sm font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                        data-action="click->notification-center#bulkMarkUnread">
                    Mark Unread
                </button>
                <button type="button" 
                        class="inline-flex items-center px-3 py-1 border border-gray-300 text-sm font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                        data-action="click->notification-center#bulkArchive">
                    Archive
                </button>
                <button type="button" 
                        class="inline-flex items-center px-3 py-1 border border-red-300 text-sm font-medium rounded text-red-700 bg-white hover:bg-red-50"
                        data-action="click->notification-center#bulkDelete">
                    Delete
                </button>
            </div>
        </div>
    </div>

    <!-- Notifications List -->
    <div class="space-y-4" data-notification-center-target="notificationsList">
        {% for notification in notifications %}
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 
                    {% if not notification.is_read %}border-l-4 border-l-blue-500{% endif %}"
             data-notification-id="{{ notification.id }}">
            
            <div class="flex items-start">
                <!-- Selection Checkbox -->
                <input type="checkbox" 
                       class="mt-1 mr-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                       data-notification-center-target="notificationCheckbox"
                       data-notification-id="{{ notification.id }}"
                       data-action="change->notification-center#updateSelection">

                <!-- Category Icon -->
                <div class="flex-shrink-0 mr-4">
                    <div class="w-10 h-10 rounded-full bg-{{ notification.category.color }}-100 flex items-center justify-center">
                        <i class="{{ notification.category.icon }} text-{{ notification.category.color }}-600"></i>
                    </div>
                </div>

                <!-- Content -->
                <div class="flex-1 min-w-0">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900">{{ notification.title }}</h3>
                        <div class="flex items-center space-x-2">
                            {% if notification.priority == 'high' or notification.priority == 'urgent' %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                         {% if notification.priority == 'urgent' %}bg-red-100 text-red-800
                                         {% else %}bg-orange-100 text-orange-800{% endif %}">
                                {{ notification.get_priority_display }}
                            </span>
                            {% endif %}
                            
                            <span class="text-sm text-gray-500">{{ notification.created_at|timesince }} ago</span>
                        </div>
                    </div>
                    
                    <p class="text-gray-700 mt-2">{{ notification.message }}</p>
                    
                    <div class="mt-4 flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <span class="inline-flex items-center text-sm text-gray-500">
                                <i class="{{ notification.category.icon }} mr-1"></i>
                                {{ notification.category.display_name }}
                            </span>
                            
                            {% if not notification.is_read %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                Unread
                            </span>
                            {% endif %}
                        </div>
                        
                        <div class="flex items-center space-x-2">
                            {% if notification.action_url %}
                            <a href="{{ notification.action_url }}" 
                               class="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded text-blue-600 hover:text-blue-800"
                               data-action="click->notification-center#handleActionClick"
                               data-notification-id="{{ notification.id }}">
                                {{ notification.action_text|default:"View" }}
                            </a>
                            {% endif %}
                            
                            {% if notification.is_read %}
                            <button type="button" 
                                    class="text-sm text-gray-500 hover:text-gray-700"
                                    data-action="click->notification-center#markAsUnread"
                                    data-notification-id="{{ notification.id }}">
                                Mark as Unread
                            </button>
                            {% else %}
                            <button type="button" 
                                    class="text-sm text-blue-600 hover:text-blue-800"
                                    data-action="click->notification-center#markAsRead"
                                    data-notification-id="{{ notification.id }}">
                                Mark as Read
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM9 7H4l5-5v5zm6 10V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2h6a2 2 0 002-2z"/>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No notifications</h3>
            <p class="mt-1 text-sm text-gray-500">You're all caught up!</p>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if notifications.has_other_pages %}
    <div class="mt-8">
        <nav class="flex items-center justify-between">
            <div class="flex-1 flex justify-between sm:hidden">
                {% if notifications.has_previous %}
                <a href="?page={{ notifications.previous_page_number }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Previous
                </a>
                {% endif %}
                {% if notifications.has_next %}
                <a href="?page={{ notifications.next_page_number }}" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Next
                </a>
                {% endif %}
            </div>
            
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700">
                        Showing <span class="font-medium">{{ notifications.start_index }}</span> to 
                        <span class="font-medium">{{ notifications.end_index }}</span> of 
                        <span class="font-medium">{{ notifications.paginator.count }}</span> results
                    </p>
                </div>
                
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                        {% if notifications.has_previous %}
                        <a href="?page={{ notifications.previous_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            Previous
                        </a>
                        {% endif %}
                        
                        {% for num in notifications.paginator.page_range %}
                        {% if notifications.number == num %}
                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">
                            {{ num }}
                        </span>
                        {% else %}
                        <a href="?page={{ num }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                            {{ num }}
                        </a>
                        {% endif %}
                        {% endfor %}
                        
                        {% if notifications.has_next %}
                        <a href="?page={{ notifications.next_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            Next
                        </a>
                        {% endif %}
                    </nav>
                </div>
            </div>
        </nav>
    </div>
    {% endif %}
</div>

<!-- Toast Notifications Container -->
<div id="toast-container" 
     class="fixed top-4 right-4 z-50 space-y-2"
     data-notification-center-target="toastContainer">
</div>
{% endblock %}

{% block extra_js %}
<script>
// Request notification permission on page load
if ('Notification' in window && Notification.permission === 'default') {
    Notification.requestPermission();
}
</script>
{% endblock %}
```

### **Step 5: Stimulus Controller for Notification Center**

```javascript
// frontend/src/controllers/notification_center_controller.js
import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = [
    "notificationsList", "bulkActions", "selectAll", "selectedCount",
    "notificationCheckbox", "toastContainer"
  ]
  
  static values = {
    wsUrl: String,
    markReadUrl: String,
    bulkActionUrl: String
  }
  
  connect() {
    this.selectedNotifications = new Set()
    this.connectWebSocket()
    this.setupNotificationPermission()
  }
  
  disconnect() {
    if (this.websocket) {
      this.websocket.close()
    }
  }
  
  // WebSocket connection
  connectWebSocket() {
    if (!this.wsUrlValue) return
    
    this.websocket = new WebSocket(this.wsUrlValue)
    
    this.websocket.onopen = () => {
      console.log('Notification WebSocket connected')
    }
    
    this.websocket.onmessage = (event) => {
      const data = JSON.parse(event.data)
      this.handleWebSocketMessage(data)
    }
    
    this.websocket.onclose = () => {
      console.log('Notification WebSocket disconnected')
      // Attempt to reconnect after 5 seconds
      setTimeout(() => this.connectWebSocket(), 5000)
    }
    
    this.websocket.onerror = (error) => {
      console.error('WebSocket error:', error)
    }
  }
  
  // Handle WebSocket messages
  handleWebSocketMessage(data) {
    switch (data.type) {
      case 'new_notification':
        this.handleNewNotification(data.notification)
        break
      case 'unread_count':
        this.updateUnreadCount(data.count)
        break
      case 'notifications_list':
        this.updateNotificationsList(data)
        break
    }
  }
  
  // Handle new notification
  handleNewNotification(notification) {
    // Show toast notification
    this.showToast(notification)
    
    // Show browser notification if permission granted
    this.showBrowserNotification(notification)
    
    // Play notification sound (optional)
    this.playNotificationSound()
    
    // Add to list if on first page
    this.prependNotificationToList(notification)
  }
  
  // Show toast notification
  showToast(notification) {
    const toast = document.createElement('div')
    toast.className = `
      bg-white border border-gray-200 rounded-lg shadow-lg p-4 mb-2 
      transform transition-all duration-300 translate-x-full
    `
    
    toast.innerHTML = `
      <div class="flex items-start">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 rounded-full bg-${notification.category.color}-100 flex items-center justify-center">
            <i class="${notification.category.icon} text-${notification.category.color}-600 text-sm"></i>
          </div>
        </div>
        <div class="ml-3 flex-1">
          <p class="text-sm font-medium text-gray-900">${notification.title}</p>
          <p class="text-sm text-gray-500 mt-1">${notification.message}</p>
        </div>
        <button type="button" 
                class="ml-4 text-gray-400 hover:text-gray-600"
                onclick="this.parentElement.parentElement.remove()">
          <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
          </svg>
        </button>
      </div>
    `
    
    this.toastContainerTarget.appendChild(toast)
    
    // Animate in
    setTimeout(() => {
      toast.classList.remove('translate-x-full')
    }, 100)
    
    // Auto remove after 5 seconds
    setTimeout(() => {
      toast.classList.add('translate-x-full')
      setTimeout(() => toast.remove(), 300)
    }, 5000)
  }
  
  // Show browser notification
  showBrowserNotification(notification) {
    if ('Notification' in window && Notification.permission === 'granted') {
      const browserNotification = new Notification(notification.title, {
        body: notification.message,
        icon: '/static/images/logo-icon.png',
        tag: notification.id
      })
      
      browserNotification.onclick = () => {
        window.focus()
        if (notification.action_url) {
          window.location.href = notification.action_url
        }
        browserNotification.close()
      }
      
      // Auto close after 5 seconds
      setTimeout(() => browserNotification.close(), 5000)
    }
  }
  
  // Play notification sound
  playNotificationSound() {
    const audio = new Audio('/static/sounds/notification.mp3')
    audio.volume = 0.3
    audio.play().catch(() => {
      // Ignore errors (user might not have interacted with page yet)
    })
  }
  
  // Mark notification as read
  async markAsRead(event) {
    const notificationId = event.currentTarget.dataset.notificationId
    
    try {
      const response = await fetch(
        this.markReadUrlValue.replace('0', notificationId),
        {
          method: 'POST',
          headers: {
            'X-CSRFToken': this.getCSRFToken(),
            'Content-Type': 'application/json'
          }
        }
      )
      
      if (response.ok) {
        const data = await response.json()
        this.updateNotificationReadState(notificationId, true)
        this.updateUnreadCount(data.unread_count)
      }
    } catch (error) {
      console.error('Error marking notification as read:', error)
    }
  }
  
  // Mark notification as unread
  async markAsUnread(event) {
    const notificationId = event.currentTarget.dataset.notificationId
    
    try {
      const response = await fetch(`/notifications/${notificationId}/mark-unread/`, {
        method: 'POST',
        headers: {
          'X-CSRFToken': this.getCSRFToken(),
          'Content-Type': 'application/json'
        }
      })
      
      if (response.ok) {
        const data = await response.json()
        this.updateNotificationReadState(notificationId, false)
        this.updateUnreadCount(data.unread_count)
      }
    } catch (error) {
      console.error('Error marking notification as unread:', error)
    }
  }
  
  // Mark all as read
  async markAllAsRead() {
    try {
      const response = await fetch('/notifications/mark-all-read/', {
        method: 'POST',
        headers: {
          'X-CSRFToken': this.getCSRFToken(),
          'Content-Type': 'application/json'
        }
      })
      
      if (response.ok) {
        // Reload page to show updated state
        window.location.reload()
      }
    } catch (error) {
      console.error('Error marking all as read:', error)
    }
  }
  
  // Selection management
  updateSelection(event) {
    const checkbox = event.target
    const notificationId = checkbox.dataset.notificationId
    
    if (checkbox.checked) {
      this.selectedNotifications.add(notificationId)
    } else {
      this.selectedNotifications.delete(notificationId)
    }
    
    this.updateBulkActionsVisibility()
    this.updateSelectedCount()
    this.updateSelectAllState()
  }
  
  toggleSelectAll(event) {
    const selectAll = event.target.checked
    
    this.notificationCheckboxTargets.forEach(checkbox => {
      checkbox.checked = selectAll
      const notificationId = checkbox.dataset.notificationId
      
      if (selectAll) {
        this.selectedNotifications.add(notificationId)
      } else {
        this.selectedNotifications.delete(notificationId)
      }
    })
    
    this.updateBulkActionsVisibility()
    this.updateSelectedCount()
  }
  
  updateBulkActionsVisibility() {
    const hasSelected = this.selectedNotifications.size > 0
    this.bulkActionsTarget.style.display = hasSelected ? 'flex' : 'none'
  }
  
  updateSelectedCount() {
    this.selectedCountTarget.textContent = this.selectedNotifications.size
  }
  
  updateSelectAllState() {
    const totalCheckboxes = this.notificationCheckboxTargets.length
    const selectedCount = this.selectedNotifications.size
    
    if (selectedCount === 0) {
      this.selectAllTarget.indeterminate = false
      this.selectAllTarget.checked = false
    } else if (selectedCount === totalCheckboxes) {
      this.selectAllTarget.indeterminate = false
      this.selectAllTarget.checked = true
    } else {
      this.selectAllTarget.indeterminate = true
      this.selectAllTarget.checked = false
    }
  }
  
  // Bulk actions
  async bulkMarkRead() {
    await this.performBulkAction('mark_read')
  }
  
  async bulkMarkUnread() {
    await this.performBulkAction('mark_unread')
  }
  
  async bulkArchive() {
    await this.performBulkAction('archive')
  }
  
  async bulkDelete() {
    if (confirm('Are you sure you want to delete the selected notifications?')) {
      await this.performBulkAction('delete')
    }
  }
  
  async performBulkAction(action) {
    const notificationIds = Array.from(this.selectedNotifications)
    
    try {
      const response = await fetch(this.bulkActionUrlValue, {
        method: 'POST',
        headers: {
          'X-CSRFToken': this.getCSRFToken(),
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: action,
          notification_ids: notificationIds
        })
      })
      
      if (response.ok) {
        // Reload page to show updated state
        window.location.reload()
      }
    } catch (error) {
      console.error('Error performing bulk action:', error)
    }
  }
  
  // Filtering
  filterByCategory(event) {
    const category = event.target.value
    const url = new URL(window.location)
    
    if (category) {
      url.searchParams.set('category', category)
    } else {
      url.searchParams.delete('category')
    }
    
    window.location.href = url.toString()
  }
  
  filterByStatus(event) {
    const status = event.target.value
    const url = new URL(window.location)
    
    if (status && status !== 'all') {
      url.searchParams.set('status', status)
    } else {
      url.searchParams.delete('status')
    }
    
    window.location.href = url.toString()
  }
  
  // Handle action clicks
  handleActionClick(event) {
    const notificationId = event.currentTarget.dataset.notificationId
    
    // Mark as read when action is clicked
    fetch(this.markReadUrlValue.replace('0', notificationId), {
      method: 'POST',
      headers: {
        'X-CSRFToken': this.getCSRFToken(),
        'Content-Type': 'application/json'
      }
    }).catch(error => {
      console.error('Error marking notification as read:', error)
    })
  }
  
  // Helper methods
  updateNotificationReadState(notificationId, isRead) {
    const notificationElement = document.querySelector(`[data-notification-id="${notificationId}"]`)
    if (notificationElement) {
      if (isRead) {
        notificationElement.classList.remove('border-l-4', 'border-l-blue-500')
      } else {
        notificationElement.classList.add('border-l-4', 'border-l-blue-500')
      }
    }
  }
  
  updateUnreadCount(count) {
    // Update badge in navigation
    const badge = document.querySelector('[data-notification-badge]')
    if (badge) {
      if (count > 0) {
        badge.textContent = count > 99 ? '99+' : count
        badge.style.display = 'inline'
      } else {
        badge.style.display = 'none'
      }
    }
  }
  
  prependNotificationToList(notification) {
    // Only add if we're on the first page and showing all notifications
    const url = new URL(window.location)
    const page = url.searchParams.get('page')
    const category = url.searchParams.get('category')
    const status = url.searchParams.get('status')
    
    if ((!page || page === '1') && !category && (!status || status === 'all')) {
      // Create notification element and prepend to list
      // Implementation would create the HTML element and insert it
    }
  }
  
  setupNotificationPermission() {
    if ('Notification' in window && Notification.permission === 'default') {
      // Show a subtle prompt to enable notifications
      this.showNotificationPermissionPrompt()
    }
  }
  
  showNotificationPermissionPrompt() {
    const prompt = document.createElement('div')
    prompt.className = 'bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6'
    prompt.innerHTML = `
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
          </svg>
        </div>
        <div class="ml-3 flex-1">
          <h3 class="text-sm font-medium text-blue-800">Enable Browser Notifications</h3>
          <p class="text-sm text-blue-700 mt-1">
            Get instant notifications for new messages and updates.
          </p>
        </div>
        <div class="ml-3">
          <button type="button" 
                  class="bg-blue-100 text-blue-800 px-3 py-1 rounded text-sm hover:bg-blue-200"
                  onclick="this.parentElement.parentElement.parentElement.remove(); Notification.requestPermission()">
            Enable
          </button>
          <button type="button" 
                  class="ml-2 text-blue-600 text-sm hover:text-blue-800"
                  onclick="this.parentElement.parentElement.parentElement.remove()">
            Dismiss
          </button>
        </div>
      </div>
    `
    
    // Insert at the top of the page
    const container = document.querySelector('[data-controller="notification-center"]')
    container.insertBefore(prompt, container.firstChild)
  }
  
  getCSRFToken() {
    return document.querySelector('[name=csrfmiddlewaretoken]')?.value ||
           document.querySelector('meta[name=csrf-token]')?.getAttribute('content') ||
           ''
  }
}
```

## ✅ Expected Outcome

After completing this exercise, you should have:

1. **Real-time Notification Center**: Live updates without page refresh
2. **Comprehensive Management**: Read/unread states, bulk actions, filtering
3. **Multiple Notification Types**: Toast, browser, and in-app notifications
4. **User Preferences**: Customizable notification settings
5. **WebSocket Integration**: Real-time communication with the server
6. **Responsive Design**: Works seamlessly across all devices

## 🚀 Extension Challenges

1. **Push Notifications**: Implement service worker for offline notifications
2. **Email Digests**: Create daily/weekly notification summaries
3. **Smart Grouping**: Group related notifications together
4. **Notification Templates**: Create reusable notification templates
5. **Analytics Dashboard**: Track notification engagement and effectiveness

## 🔍 Testing Your Implementation

1. **Real-time Testing**: Verify notifications appear instantly
2. **Cross-browser Testing**: Test WebSocket connections across browsers
3. **Mobile Testing**: Ensure touch interactions work properly
4. **Performance Testing**: Check with large numbers of notifications
5. **Edge Cases**: Test connection drops, reconnection, etc.

## 💡 Solution Tips

- Implement proper WebSocket reconnection logic
- Use efficient database queries with proper indexing
- Add proper error handling for network issues
- Consider implementing notification batching for high-volume scenarios
- Use CSS transitions for smooth animations

Outstanding work! You've built a sophisticated notification system that provides users with real-time updates and comprehensive management tools. This exercise demonstrated advanced WebSocket usage, complex state management, and creating polished user interfaces.

**Next**: [Exercise 4: Analytics Dashboard](./exercise-4-analytics.md)
